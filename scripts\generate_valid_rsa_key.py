#!/usr/bin/env python3
"""
Generate a mathematically valid RSA key pair for the client-server system
This will create a real RSA key that PyCryptodome can import successfully
"""

from Crypto.PublicKey import RSA
from Crypto.Cipher import PKCS1_OAEP
import binas<PERSON><PERSON>

def generate_rsa_key_pair():
    """Generate a valid 1024-bit RSA key pair"""
    print("Generating 1024-bit RSA key pair...")
    
    # Generate RSA key pair
    key = RSA.generate(1024)
    
    # Export public key in DER format
    public_der = key.publickey().export_key('DER')
    
    # Export private key in DER format  
    private_der = key.export_key('DER')
    
    print(f"Public key DER size: {len(public_der)} bytes")
    print(f"Private key DER size: {len(private_der)} bytes")
    
    # Test that the key works
    print("\nTesting RSA key functionality...")
    test_message = b"Hello, RSA!"
    
    # Encrypt with public key
    cipher_rsa = PKCS1_OAEP.new(key.publickey())
    encrypted = cipher_rsa.encrypt(test_message)
    print(f"Encrypted message size: {len(encrypted)} bytes")
    
    # Decrypt with private key
    cipher_rsa = PKCS1_OAEP.new(key)
    decrypted = cipher_rsa.decrypt(encrypted)
    print(f"Decrypted message: {decrypted}")
    
    if decrypted == test_message:
        print("[OK] RSA key pair is working correctly!")
    else:
        print("[FAIL] RSA key pair test failed!")
        return None, None
    
    return public_der, private_der

def format_as_cpp_array(data, name):
    """Format binary data as C++ array initialization"""
    hex_values = [f"0x{b:02x}" for b in data]
    
    # Format as C++ array with proper line breaks
    lines = []
    line = "        std::vector<uint8_t> " + name + " = {"
    
    for i, hex_val in enumerate(hex_values):
        if i > 0:
            line += ","
        if len(line) > 100:  # Break line if too long
            lines.append(line)
            line = "            " + hex_val
        else:
            if i > 0:
                line += " "
            line += hex_val
    
    line += "\n        };"
    lines.append(line)
    
    return "\n".join(lines)

def main():
    print("RSA Key Generation for Client-Server Encrypted Backup Framework")
    print("=" * 70)
    
    # Generate valid RSA key pair
    public_der, private_der = generate_rsa_key_pair()
    
    if public_der is None:
        print("[FAIL] Failed to generate valid RSA key pair")
        return
    
    print("\n" + "=" * 70)
    print("C++ CODE FOR RSAWrapper.cpp:")
    print("=" * 70)
    
    # Format public key for C++
    print("\n// Replace the derKey vector in RSAWrapper.cpp with this:")
    print(format_as_cpp_array(public_der, "derKey"))
    
    print(f"\n// Key size verification: {len(public_der)} bytes")
    print("// This is a mathematically valid 1024-bit RSA public key in DER format")
    print("// Generated by PyCryptodome and verified to work with PKCS1_OAEP encryption")
    
    # Save keys to files for reference
    with open("valid_public_key.der", "wb") as f:
        f.write(public_der)
    
    with open("valid_private_key.der", "wb") as f:
        f.write(private_der)
    
    print(f"\n[OK] Keys saved to valid_public_key.der ({len(public_der)} bytes)")
    print(f"[OK] Keys saved to valid_private_key.der ({len(private_der)} bytes)")
    
    # Test import with PyCryptodome
    print("\nTesting PyCryptodome import...")
    try:
        imported_key = RSA.import_key(public_der)
        print("[OK] PyCryptodome can successfully import the public key")
        print(f"[OK] Key size: {imported_key.size_in_bits()} bits")
        print(f"[OK] Key modulus: {hex(imported_key.n)[:50]}...")
    except Exception as e:
        print(f"[FAIL] PyCryptodome import failed: {e}")
    
    print("\n" + "=" * 70)
    print("NEXT STEPS:")
    print("1. Copy the derKey vector above into client/src/RSAWrapper.cpp")
    print("2. Rebuild the client")
    print("3. Test the complete system")
    print("=" * 70)

if __name__ == "__main__":
    main()
