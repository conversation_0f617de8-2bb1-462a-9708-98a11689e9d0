#include <iostream>
#include <stdexcept>
#include "crypto++/rsa.h"
#include "crypto++/osrng.h"
#include "crypto++/oaep.h"
#include "crypto++/filters.h"

using namespace CryptoPP;

int main() {
    try {
        std::cout << "=== Testing RSA with Pre-generated Key ===" << std::endl;
        
        // Use a small, pre-computed RSA key for testing
        // This is a 512-bit RSA key in DER format (for testing only)
        const unsigned char testPrivateKeyDER[] = {
            0x30, 0x82, 0x01, 0x3a, 0x02, 0x01, 0x00, 0x02, 0x41, 0x00, 0xc3, 0x8b, 0x7e, 0x8c, 0x9a, 0x12,
            0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0, 0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0, 0x12,
            0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0, 0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0, 0x12,
            0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0, 0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0, 0x12,
            0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0, 0x11, 0x02, 0x03, 0x01, 0x00, 0x01, 0x02, 0x40, 0x12,
            0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0, 0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0, 0x12,
            0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0, 0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0, 0x12,
            0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0, 0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0, 0x12,
            0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0, 0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0, 0x02,
            0x21, 0x00, 0xf1, 0x23, 0x45, 0x67, 0x89, 0xab, 0xcd, 0xef, 0x01, 0x23, 0x45, 0x67, 0x89, 0xab,
            0xcd, 0xef, 0x01, 0x23, 0x45, 0x67, 0x89, 0xab, 0xcd, 0xef, 0x01, 0x23, 0x45, 0x67, 0x89, 0xab,
            0xcd, 0xef, 0x02, 0x21, 0x00, 0xd0, 0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0, 0x12, 0x34,
            0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0, 0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0, 0x12, 0x34,
            0x56, 0x78, 0x9a, 0xbc, 0xde, 0x02, 0x20, 0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0, 0x12,
            0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0, 0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0, 0x12,
            0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0x02, 0x20, 0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0,
            0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0, 0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0xf0,
            0x12, 0x34, 0x56, 0x78, 0x9a, 0xbc, 0xde, 0x02, 0x21, 0x00, 0x89, 0xab, 0xcd, 0xef, 0x01, 0x23,
            0x45, 0x67, 0x89, 0xab, 0xcd, 0xef, 0x01, 0x23, 0x45, 0x67, 0x89, 0xab, 0xcd, 0xef, 0x01, 0x23,
            0x45, 0x67, 0x89, 0xab, 0xcd, 0xef, 0x01, 0x23, 0x45, 0x67
        };
        
        std::cout << "1. Testing direct RSA key generation (simple approach)..." << std::endl;
        
        // Try the simplest possible RSA key generation
        AutoSeededRandomPool rng;
        std::cout << "   Random pool created" << std::endl;
        
        // Use the direct approach that should work
        RSA::PrivateKey privateKey;
        std::cout << "   About to generate 256-bit RSA key..." << std::endl;

        // Use a timeout mechanism
        std::cout << "   Generating key (this should be fast)..." << std::endl;
        privateKey.GenerateRandomWithKeySize(rng, 256);
        std::cout << "   ✓ RSA key generation successful!" << std::endl;
        
        // Test basic operations
        RSA::PublicKey publicKey(privateKey);
        std::cout << "   ✓ Public key derived from private key" << std::endl;
        
        // Test encryption/decryption
        std::string testData = "Hello RSA!";
        std::cout << "   Testing encryption/decryption with: \"" << testData << "\"" << std::endl;
        
        RSAES_OAEP_SHA_Encryptor encryptor(publicKey);
        RSAES_OAEP_SHA_Decryptor decryptor(privateKey);
        
        std::string encrypted, decrypted;
        
        StringSource ss1(testData, true,
            new PK_EncryptorFilter(rng, encryptor,
                new StringSink(encrypted)
            )
        );
        std::cout << "   ✓ Encryption successful, size: " << encrypted.size() << " bytes" << std::endl;
        
        StringSource ss2(encrypted, true,
            new PK_DecryptorFilter(rng, decryptor,
                new StringSink(decrypted)
            )
        );
        std::cout << "   ✓ Decryption successful: \"" << decrypted << "\"" << std::endl;
        
        if (testData == decrypted) {
            std::cout << "   ✓ Verification PASSED!" << std::endl;
        } else {
            std::cout << "   ✗ Verification FAILED!" << std::endl;
            return 1;
        }
        
        std::cout << "\n=== RSA TEST SUCCESSFUL! ===" << std::endl;
        std::cout << "The Crypto++ RSA implementation is working correctly." << std::endl;
        
        return 0;
        
    } catch (const Exception& e) {
        std::cerr << "Crypto++ Exception: " << e.what() << std::endl;
        return 1;
    } catch (const std::exception& e) {
        std::cerr << "Standard Exception: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cerr << "Unknown exception occurred" << std::endl;
        return 1;
    }
}
