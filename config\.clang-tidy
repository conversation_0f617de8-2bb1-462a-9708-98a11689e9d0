---
# Clang-tidy configuration for Encrypted Backup Client
# This configuration provides a good balance of useful checks without being overly strict

Checks: >
  -*,
  bugprone-*,
  cert-*,
  clang-analyzer-*,
  cppcoreguidelines-*,
  google-*,
  hicpp-*,
  llvm-*,
  misc-*,
  modernize-*,
  performance-*,
  portability-*,
  readability-*,
  -bugprone-easily-swappable-parameters,
  -bugprone-exception-escape,
  -cert-err58-cpp,
  -cppcoreguidelines-avoid-magic-numbers,
  -cppcoreguidelines-macro-usage,
  -cppcoreguidelines-non-private-member-variables-in-classes,
  -cppcoreguidelines-owning-memory,
  -cppcoreguidelines-pro-bounds-array-to-pointer-decay,
  -cppcoreguidelines-pro-bounds-constant-array-index,
  -cppcoreguidelines-pro-bounds-pointer-arithmetic,
  -cppcoreguidelines-pro-type-const-cast,
  -cppcoreguidelines-pro-type-reinterpret-cast,
  -cppcoreguidelines-pro-type-union-access,
  -cppcoreguidelines-pro-type-vararg,
  -google-readability-avoid-underscore-in-googletest-name,
  -google-readability-todo,
  -hicpp-avoid-c-arrays,
  -hicpp-braces-around-statements,
  -hicpp-no-array-decay,
  -hicpp-signed-bitwise,
  -hicpp-special-member-functions,
  -hicpp-uppercase-literal-suffix,
  -hicpp-use-auto,
  -hicpp-vararg,
  -llvm-header-guard,
  -llvm-include-order,
  -misc-non-private-member-variables-in-classes,
  -modernize-avoid-c-arrays,
  -modernize-use-trailing-return-type,
  -readability-avoid-const-params-in-decls,
  -readability-braces-around-statements,
  -readability-convert-member-functions-to-static,
  -readability-function-cognitive-complexity,
  -readability-identifier-length,
  -readability-magic-numbers,
  -readability-named-parameter,
  -readability-uppercase-literal-suffix

CheckOptions:
  - key: readability-identifier-naming.NamespaceCase
    value: lower_case
  - key: readability-identifier-naming.ClassCase
    value: CamelCase
  - key: readability-identifier-naming.StructCase
    value: CamelCase
  - key: readability-identifier-naming.TemplateParameterCase
    value: CamelCase
  - key: readability-identifier-naming.FunctionCase
    value: camelBack
  - key: readability-identifier-naming.VariableCase
    value: camelBack
  - key: readability-identifier-naming.ClassMemberCase
    value: camelBack
  - key: readability-identifier-naming.ClassMemberSuffix
    value: _
  - key: readability-identifier-naming.PrivateMemberSuffix
    value: _
  - key: readability-identifier-naming.ProtectedMemberSuffix
    value: _
  - key: readability-identifier-naming.EnumConstantCase
    value: CamelCase
  - key: readability-identifier-naming.ConstantCase
    value: UPPER_CASE
  - key: readability-identifier-naming.StaticConstantCase
    value: UPPER_CASE
  - key: readability-identifier-naming.GlobalConstantCase
    value: UPPER_CASE
  - key: readability-identifier-naming.MacroDefinitionCase
    value: UPPER_CASE
  - key: cppcoreguidelines-special-member-functions.AllowSoleDefaultDtor
    value: true
  - key: modernize-loop-convert.MaxCopySize
    value: 16
  - key: modernize-loop-convert.MinConfidence
    value: reasonable
  - key: modernize-loop-convert.NamingStyle
    value: CamelCase
  - key: modernize-pass-by-value.IncludeStyle
    value: llvm
  - key: modernize-replace-auto-ptr.IncludeStyle
    value: llvm
  - key: cert-str34-c.DiagnoseSignedUnsignedCharComparisons
    value: false
  - key: google-readability-braces-around-statements.ShortStatementLines
    value: 1
  - key: google-readability-function-size.StatementThreshold
    value: 800
  - key: google-readability-namespace-comments.ShortNamespaceLines
    value: 10
  - key: google-readability-namespace-comments.SpacesBeforeComments
    value: 2
  - key: readability-function-size.LineThreshold
    value: 80
  - key: readability-function-size.StatementThreshold
    value: 800
  - key: readability-function-size.BranchThreshold
    value: 60

WarningsAsErrors: ''
HeaderFilterRegex: '(client/include/.*|client/src/.*)'
AnalyzeTemporaryDtors: false
FormatStyle: file
User: user
