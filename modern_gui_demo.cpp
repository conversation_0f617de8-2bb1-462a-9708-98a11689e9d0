#include <windows.h>
#include <shellapi.h>
#include <iostream>
#include <string>
#include <thread>
#include <atomic>

// Window constants
const wchar_t* WINDOW_CLASS = L"ModernBackupClient";
const wchar_t* WINDOW_TITLE = L"🚀 ULTRA MODERN Encrypted Backup Client";

// Global variables
HWND g_hWnd = nullptr;
std::atomic<bool> g_connected{false};
std::atomic<int> g_progress{25};
std::atomic<int> g_total{100};

// Forward declarations
LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam);
void DrawModernUI(HDC hdc, RECT rect);

int WINAPI WinMain(HINSTANCE hInstance, HINSTANCE hPrevInstance, LPSTR lpCmdLine, int nCmdShow) {
    // Register window class
    WNDCLASSEXW wc = {};
    wc.cbSize = sizeof(WNDCLASSEXW);
    wc.lpfnWndProc = WindowProc;
    wc.hInstance = hInstance;
    wc.hbrBackground = CreateSolidBrush(RGB(0, 32, 64)); // Dark blue background
    wc.lpszClassName = WINDOW_CLASS;
    wc.hIcon = LoadIcon(nullptr, IDI_APPLICATION);
    wc.hCursor = LoadCursor(nullptr, IDC_ARROW);
    wc.style = CS_HREDRAW | CS_VREDRAW;

    if (!RegisterClassExW(&wc)) {
        MessageBoxW(nullptr, L"Failed to register window class", L"Error", MB_OK | MB_ICONERROR);
        return 1;
    }

    // Create window
    g_hWnd = CreateWindowExW(
        0,
        WINDOW_CLASS,
        WINDOW_TITLE,
        WS_OVERLAPPEDWINDOW,
        CW_USEDEFAULT, CW_USEDEFAULT, 800, 600,
        nullptr, nullptr, hInstance, nullptr
    );

    if (!g_hWnd) {
        MessageBoxW(nullptr, L"Failed to create window", L"Error", MB_OK | MB_ICONERROR);
        return 1;
    }

    // Center window
    RECT desktop, window;
    GetWindowRect(GetDesktopWindow(), &desktop);
    GetWindowRect(g_hWnd, &window);
    int x = (desktop.right - (window.right - window.left)) / 2;
    int y = (desktop.bottom - (window.bottom - window.top)) / 2;
    SetWindowPos(g_hWnd, nullptr, x, y, 0, 0, SWP_NOSIZE | SWP_NOZORDER);

    ShowWindow(g_hWnd, nCmdShow);
    UpdateWindow(g_hWnd);

    // Simulate connection progress
    std::thread([]{
        for (int i = 0; i <= 100; i += 5) {
            g_progress = i;
            if (i == 50) g_connected = true;
            InvalidateRect(g_hWnd, nullptr, TRUE);
            Sleep(200);
        }
    }).detach();

    // Message loop
    MSG msg;
    while (GetMessage(&msg, nullptr, 0, 0)) {
        TranslateMessage(&msg);
        DispatchMessage(&msg);
    }

    return static_cast<int>(msg.wParam);
}

LRESULT CALLBACK WindowProc(HWND hwnd, UINT uMsg, WPARAM wParam, LPARAM lParam) {
    switch (uMsg) {
        case WM_PAINT: {
            PAINTSTRUCT ps;
            HDC hdc = BeginPaint(hwnd, &ps);
            RECT rect;
            GetClientRect(hwnd, &rect);
            DrawModernUI(hdc, rect);
            EndPaint(hwnd, &ps);
            return 0;
        }
        
        case WM_DESTROY:
            PostQuitMessage(0);
            return 0;
            
        case WM_LBUTTONDOWN: {
            // Simulate reconnection
            g_connected = !g_connected;
            g_progress = g_connected ? 100 : 0;
            InvalidateRect(hwnd, nullptr, TRUE);
            return 0;
        }
    }
    return DefWindowProc(hwnd, uMsg, wParam, lParam);
}

void DrawModernUI(HDC hdc, RECT rect) {
    // Create gradient background
    TRIVERTEX vertices[2];
    vertices[0].x = 0;
    vertices[0].y = 0;
    vertices[0].Red = 0x0000;
    vertices[0].Green = 0x2000;
    vertices[0].Blue = 0x4000;
    vertices[0].Alpha = 0x0000;

    vertices[1].x = rect.right;
    vertices[1].y = rect.bottom;
    vertices[1].Red = 0x1000;
    vertices[1].Green = 0x3000;
    vertices[1].Blue = 0x8000;
    vertices[1].Alpha = 0x0000;

    GRADIENT_RECT gradientRect;
    gradientRect.UpperLeft = 0;
    gradientRect.LowerRight = 1;

    GradientFill(hdc, vertices, 2, &gradientRect, 1, GRADIENT_FILL_RECT_V);

    // Set text properties
    SetBkMode(hdc, TRANSPARENT);
    HFONT hTitleFont = CreateFontW(36, 0, 0, 0, FW_BOLD, FALSE, FALSE, FALSE,
                                   DEFAULT_CHARSET, OUT_DEFAULT_PRECIS,
                                   CLIP_DEFAULT_PRECIS, ANTIALIASED_QUALITY,
                                   DEFAULT_PITCH | FF_DONTCARE, L"Segoe UI");
    HFONT hFont = CreateFontW(18, 0, 0, 0, FW_NORMAL, FALSE, FALSE, FALSE,
                              DEFAULT_CHARSET, OUT_DEFAULT_PRECIS,
                              CLIP_DEFAULT_PRECIS, ANTIALIASED_QUALITY,
                              DEFAULT_PITCH | FF_DONTCARE, L"Segoe UI");

    // Title
    SelectObject(hdc, hTitleFont);
    SetTextColor(hdc, RGB(255, 255, 255));
    std::wstring title = L"🚀 ULTRA MODERN Backup Client 🎨";
    TextOutW(hdc, 50, 50, title.c_str(), static_cast<int>(title.length()));

    // Connection status
    SelectObject(hdc, hFont);
    bool connected = g_connected.load();
    std::wstring status = connected ? L"🟢 CONNECTED TO SERVER" : L"🔴 CONNECTING...";
    SetTextColor(hdc, connected ? RGB(0, 255, 0) : RGB(255, 100, 100));
    TextOutW(hdc, 50, 120, status.c_str(), static_cast<int>(status.length()));

    // Phase
    SetTextColor(hdc, RGB(200, 200, 255));
    std::wstring phase = connected ? L"📋 Phase: File Transfer Active" : L"📋 Phase: Establishing Connection";
    TextOutW(hdc, 50, 160, phase.c_str(), static_cast<int>(phase.length()));

    // Progress
    int progress = g_progress.load();
    int total = g_total.load();
    std::wstring progText = L"📊 Progress: " + std::to_wstring(progress) + L"/" + std::to_wstring(total) + L" (" + std::to_wstring((progress * 100) / total) + L"%)";
    TextOutW(hdc, 50, 200, progText.c_str(), static_cast<int>(progText.length()));

    // Modern progress bar with glow effect
    RECT progRect = {50, 240, rect.right - 50, 270};
    
    // Outer glow
    HBRUSH glowBrush = CreateSolidBrush(RGB(0, 100, 200));
    RECT glowRect = {progRect.left - 2, progRect.top - 2, progRect.right + 2, progRect.bottom + 2};
    FillRect(hdc, &glowRect, glowBrush);
    DeleteObject(glowBrush);
    
    // Background
    HBRUSH bgBrush = CreateSolidBrush(RGB(40, 40, 60));
    FillRect(hdc, &progRect, bgBrush);
    DeleteObject(bgBrush);

    // Progress fill with gradient
    if (progress > 0) {
        RECT fillRect = progRect;
        fillRect.right = fillRect.left + ((fillRect.right - fillRect.left) * progress) / total;
        
        TRIVERTEX progVertices[2];
        progVertices[0].x = fillRect.left;
        progVertices[0].y = fillRect.top;
        progVertices[0].Red = 0x0000;
        progVertices[0].Green = 0x8000;
        progVertices[0].Blue = 0xFF00;
        progVertices[0].Alpha = 0x0000;

        progVertices[1].x = fillRect.right;
        progVertices[1].y = fillRect.bottom;
        progVertices[1].Red = 0x0000;
        progVertices[1].Green = 0xFF00;
        progVertices[1].Blue = 0x8000;
        progVertices[1].Alpha = 0x0000;

        GRADIENT_RECT progGradient;
        progGradient.UpperLeft = 0;
        progGradient.LowerRight = 1;

        GradientFill(hdc, progVertices, 2, &progGradient, 1, GRADIENT_FILL_RECT_H);
    }

    // Speed and ETA
    SetTextColor(hdc, RGB(150, 255, 150));
    std::wstring speed = L"🚀 Speed: " + std::to_wstring(progress * 10) + L" KB/s";
    TextOutW(hdc, 50, 300, speed.c_str(), static_cast<int>(speed.length()));

    std::wstring eta = L"⏱️ ETA: " + std::to_wstring((100 - progress) / 10) + L" seconds remaining";
    TextOutW(hdc, 50, 330, eta.c_str(), static_cast<int>(eta.length()));

    // Instructions
    SetTextColor(hdc, RGB(255, 255, 150));
    std::wstring instruction = L"💡 Click anywhere to toggle connection status";
    TextOutW(hdc, 50, 400, instruction.c_str(), static_cast<int>(instruction.length()));

    // Clean up fonts
    DeleteObject(hTitleFont);
    DeleteObject(hFont);
}
