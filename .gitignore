# Root .gitignore for Encrypted Backup Framework

# ==== PYTHON ====
# Virtual environments
.venv/
venv/
env/
ENV/
.env/

# Python cache and compiled files
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# ==== C++ BUILD ARTIFACTS ====
# Build directories
build/
msvc-debug/
x64/
Debug/
Release/
lib/Debug/
lib/Release/
client.dir/
CMakeFiles/
CMakeCache.txt
cmake_install.cmake
install_manifest.txt

# Visual Studio files
*.vcxproj*
*.sln*
ALL_BUILD.vcxproj*
ZERO_CHECK.vcxproj*
EncryptedBackupClient.sln*
.vs/

# Object and binary files
*.obj
*.exe
*.dll
*.lib
*.a
*.so
*.dylib
*.ilk
*.pdb
*.tlog
*.exp
*.idb
*.ipdb
*.iobj
*.ipdb

# ==== DEPENDENCIES AND THIRD-PARTY ====
# Third-party libraries (crypto++, etc.)
third_party/
vendor/
external/
deps/

# Package managers
node_modules/
.npm/
.yarn/

# ==== DATA AND LOGS ====
# Runtime data and logs
/data/
received_files/
archive/
benchmarks/
*.log
*.out
server.log
defensive.db

# Test files and temporary data
test_file.txt
*.tmp
*.temp

# ==== SECURITY AND KEYS ====
# Private keys and sensitive info
*.key
*.der
*.pem
*.p12
*.pfx
priv.key
transfer.info
client/priv.key
client/transfer.info
data/priv.key
data/transfer.info

# ==== IDE AND EDITOR SETTINGS ====
# VS Code
.vscode/
*.code-workspace

# Other editors
.idea/
*.swp
*.swo
*~
.project
.settings/

# ==== OS FILES ====
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# ==== BACKUP AND TEMPORARY FILES ====
*.bak
*~
*.backup
_ZENTASKS

# ==== OUTPUT AND GENERATED FILES ====
# Test and output files (but keep essential docs)
*.json
!package.json
!tsconfig.json
!.vscode/tasks.json
!.vscode/launch.json

# Documentation exceptions
*.md
!README.md
!CHANGELOG.md
!LICENSE.md
!docs/*.md
!.github/*.md
