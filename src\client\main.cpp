#include <iostream>
#include "../../include/client/ClientGUI.h"

// Forward declaration - we need to include the actual client class
class Client;

// Declare the actual client run function
extern bool runBackupClient();

int main() {
    std::cout << "🔒 Encrypted Backup Client v3.0 - Starting...\n";
    
    try {
        // Run the actual backup client instead of just GUI test
        if (runBackupClient()) {
            std::cout << "✅ Client completed successfully!\n";
            return 0;
        } else {
            std::cout << "❌ Client failed or was interrupted.\n";
            return 1;
        }
    } catch (const std::exception& e) {
        std::cout << "💥 Critical error: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cout << "💥 Unknown critical error occurred.\n";
        return 1;
    }
}
